package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置结构
type Config struct {
	Server struct {
		Host string `yaml:"host"`
		Port int    `yaml:"port"`
		Mode string `yaml:"mode"`
	} `yaml:"server"`

	EdgeAPI struct {
		Endpoint  string `yaml:"endpoint"`
		Timeout   string `yaml:"timeout"`
		Keepalive struct {
			Time                string `yaml:"time"`
			Timeout             string `yaml:"timeout"`
			PermitWithoutStream bool   `yaml:"permit_without_stream"`
		} `yaml:"keepalive"`
	} `yaml:"edgeapi"`

	APINode struct {
		NodeID string `yaml:"node_id"`
		Secret string `yaml:"secret"`
	} `yaml:"api_node"`

	Logging struct {
		Level  string `yaml:"level"`
		Format string `yaml:"format"`
		Output string `yaml:"output"`
	} `yaml:"logging"`

	CORS struct {
		AllowedOrigins   []string `yaml:"allowed_origins"`
		AllowedMethods   []string `yaml:"allowed_methods"`
		AllowedHeaders   []string `yaml:"allowed_headers"`
		ExposeHeaders    []string `yaml:"expose_headers"`
		AllowCredentials bool     `yaml:"allow_credentials"`
	} `yaml:"cors"`

	RateLimit struct {
		Enabled           bool `yaml:"enabled"`
		RequestsPerMinute int  `yaml:"requests_per_minute"`
		Burst             int  `yaml:"burst"`
	} `yaml:"rate_limit"`

	Security struct {
		RequestTimeout string `yaml:"request_timeout"`
		MaxRequestSize string `yaml:"max_request_size"`
		Secret         string `yaml:"secret"` // 用于CSRF token生成的密钥
		EncryptMethod   string `yaml:"encrypt_method"` // 加密方法
	} `yaml:"security"`
}

var globalConfig *Config

// LoadConfig 从指定文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定路径，尝试默认位置
	if configPath == "" {
		configPath = findConfigFile()
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	globalConfig = &config
	return &config, nil
}

// GetConfig 获取全局配置实例
func GetConfig() *Config {
	return globalConfig
}

// findConfigFile 查找配置文件
func findConfigFile() string {
	// 按优先级查找配置文件
	candidates := []string{
		"configs/config.yaml",
		"config.yaml",
		"./configs/config.yaml",
		"../configs/config.yaml",
	}

	for _, path := range candidates {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return "configs/config.yaml" // 默认路径
}

// setDefaults 设置默认配置值
func setDefaults(config *Config) {
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "release"
	}

	if config.EdgeAPI.Endpoint == "" {
		config.EdgeAPI.Endpoint = "localhost:8001"
	}
	if config.EdgeAPI.Timeout == "" {
		config.EdgeAPI.Timeout = "30s"
	}
	if config.EdgeAPI.Keepalive.Time == "" {
		config.EdgeAPI.Keepalive.Time = "10s"
	}
	if config.EdgeAPI.Keepalive.Timeout == "" {
		config.EdgeAPI.Keepalive.Timeout = "1s"
	}

	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "stdout"
	}

	if config.Security.RequestTimeout == "" {
		config.Security.RequestTimeout = "30s"
	}
	if config.Security.MaxRequestSize == "" {
		config.Security.MaxRequestSize = "10MB"
	}
	if config.Security.Secret == "" {
		config.Security.Secret = "EdgeOpenAPI_Default_Secret_Key_2024"
	}

	// CORS默认值
	if len(config.CORS.AllowedOrigins) == 0 {
		config.CORS.AllowedOrigins = []string{"*"}
	}
	if len(config.CORS.AllowedMethods) == 0 {
		config.CORS.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	}
	if len(config.CORS.AllowedHeaders) == 0 {
		config.CORS.AllowedHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	}
}

// validateConfig 验证配置有效性
func validateConfig(config *Config) error {
	if config.APINode.NodeID == "" {
		return fmt.Errorf("api_node.node_id is required")
	}
	if config.APINode.Secret == "" {
		return fmt.Errorf("api_node.secret is required")
	}

	// 验证超时配置
	if _, err := time.ParseDuration(config.EdgeAPI.Timeout); err != nil {
		return fmt.Errorf("invalid edgeapi.timeout: %w", err)
	}
	if _, err := time.ParseDuration(config.EdgeAPI.Keepalive.Time); err != nil {
		return fmt.Errorf("invalid edgeapi.keepalive.time: %w", err)
	}
	if _, err := time.ParseDuration(config.EdgeAPI.Keepalive.Timeout); err != nil {
		return fmt.Errorf("invalid edgeapi.keepalive.timeout: %w", err)
	}

	return nil
}

// GetServerAddress 获取服务器监听地址
func (c *Config) GetServerAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// GetTimeoutDuration 解析超时时间字符串为time.Duration
func (c *Config) GetTimeoutDuration() (time.Duration, error) {
	return time.ParseDuration(c.EdgeAPI.Timeout)
}

// GetKeepaliveTime 解析keepalive时间字符串为time.Duration
func (c *Config) GetKeepaliveTime() (time.Duration, error) {
	return time.ParseDuration(c.EdgeAPI.Keepalive.Time)
}

// GetKeepaliveTimeout 解析keepalive超时字符串为time.Duration
func (c *Config) GetKeepaliveTimeout() (time.Duration, error) {
	return time.ParseDuration(c.EdgeAPI.Keepalive.Timeout)
}

// GetRequestTimeout 解析请求超时时间字符串为time.Duration
func (c *Config) GetRequestTimeout() (time.Duration, error) {
	return time.ParseDuration(c.Security.RequestTimeout)
}
